"""
Solar benefit calculation logic
"""
import math

class SolarCalculator:
    def __init__(self):
        # Internal constants (not displayed in UI)
        self.AVG_IRRADIANCE = 4.0  # kWh/m²/day
        self.PERFORMANCE_RATIO = 0.75  # PR
        self.COST_PER_KW = 76000  # ₹ per kW
        self.SYSTEM_LIFETIME = 25  # years
        self.CO2_FACTOR = 0.8  # kg CO2 per unit
        self.DAYS_PER_MONTH = 30
        self.MONTHS_PER_YEAR = 12

    def calculate_monthly_consumption(self, monthly_bill, tariff_rate):
        """Calculate monthly electricity consumption from bill amount"""
        if tariff_rate <= 0:
            return 0
        return monthly_bill / tariff_rate

    def calculate_plant_capacity(self, monthly_consumption):
        """Calculate required plant capacity using: Capacity = Monthly Consumption / (Avg Irradiance × PR × 30)"""
        return monthly_consumption / (self.AVG_IRRADIANCE * self.PERFORMANCE_RATIO * self.DAYS_PER_MONTH)

    def calculate_investment_capex(self, capacity_kw):
        """Calculate investment (CAPEX) using: Investment = Capacity × Cost per kW"""
        return capacity_kw * self.COST_PER_KW

    def calculate_monthly_generation(self, capacity_kw):
        """Calculate monthly generation using: Monthly Generation = Capacity × Avg Irradiance × PR × 30"""
        return capacity_kw * self.AVG_IRRADIANCE * self.PERFORMANCE_RATIO * self.DAYS_PER_MONTH

    def calculate_yearly_generation(self, monthly_generation):
        """Calculate yearly generation using: Yearly Generation = Monthly Generation × 12"""
        return monthly_generation * self.MONTHS_PER_YEAR

    def calculate_annual_savings(self, yearly_generation, tariff_rate):
        """Calculate annual savings using: Annual Savings = Yearly Generation × Tariff"""
        return yearly_generation * tariff_rate

    def calculate_monthly_savings(self, annual_savings):
        """Calculate monthly savings from annual savings"""
        return annual_savings / self.MONTHS_PER_YEAR

    def calculate_payback_period(self, investment, annual_savings):
        """Calculate payback period using: Payback Period = Investment / Annual Savings"""
        if annual_savings <= 0:
            return float('inf')
        return investment / annual_savings

    def calculate_co2_saved_annual(self, yearly_generation):
        """Calculate CO2 saved per year using: CO₂ Saved = Yearly Generation × 0.8"""
        co2_saved_kg = yearly_generation * self.CO2_FACTOR
        return co2_saved_kg / 1000  # Convert to tons

    def calculate_co2_saved_lifetime(self, annual_co2_saved):
        """Calculate total CO2 saved over 25 years"""
        return annual_co2_saved * self.SYSTEM_LIFETIME

    def calculate_lifetime_savings(self, annual_savings):
        """Calculate total savings over system lifetime"""
        return annual_savings * self.SYSTEM_LIFETIME

    def calculate_equivalent_trees(self, annual_co2_saved):
        """Calculate equivalent trees planted (1 tree = ~22 kg CO2/year)"""
        return (annual_co2_saved * 1000) / 22  # Convert tons to kg, then divide by 22 kg/tree

    def get_comprehensive_analysis(self, monthly_bill, tariff_rate, investment_model="CAPEX"):
        """
        Get comprehensive solar analysis using accurate formulas

        Args:
            monthly_bill: Monthly electricity bill in ₹
            tariff_rate: Electricity tariff rate in ₹/unit
            investment_model: "CAPEX" or "OPEX"

        Returns:
            Dictionary with all calculations
        """
        # Step 1: Calculate monthly consumption
        monthly_consumption = self.calculate_monthly_consumption(monthly_bill, tariff_rate)

        # Step 2: Calculate plant capacity
        plant_capacity = self.calculate_plant_capacity(monthly_consumption)

        # Step 3: Calculate generation
        monthly_generation = self.calculate_monthly_generation(plant_capacity)
        yearly_generation = self.calculate_yearly_generation(monthly_generation)

        # Step 4: Calculate savings
        annual_savings = self.calculate_annual_savings(yearly_generation, tariff_rate)
        monthly_savings = self.calculate_monthly_savings(annual_savings)
        lifetime_savings = self.calculate_lifetime_savings(annual_savings)

        # Step 5: Calculate environmental impact
        annual_co2_saved = self.calculate_co2_saved_annual(yearly_generation)
        lifetime_co2_saved = self.calculate_co2_saved_lifetime(annual_co2_saved)
        equivalent_trees = self.calculate_equivalent_trees(annual_co2_saved)

        # Step 6: Calculate investment and payback (only for CAPEX)
        investment = 0
        payback_period = 0
        if investment_model == "CAPEX":
            investment = self.calculate_investment_capex(plant_capacity)
            payback_period = self.calculate_payback_period(investment, annual_savings)

        return {
            "input_data": {
                "monthly_bill": monthly_bill,
                "tariff_rate": tariff_rate,
                "investment_model": investment_model
            },
            "calculations": {
                "monthly_consumption": round(monthly_consumption, 2),
                "plant_capacity": round(plant_capacity, 2),
                "monthly_generation": round(monthly_generation, 2),
                "yearly_generation": round(yearly_generation, 0),
                "monthly_savings": round(monthly_savings, 2),
                "annual_savings": round(annual_savings, 0),
                "lifetime_savings": round(lifetime_savings, 0),
                "investment": round(investment, 0),
                "payback_period": round(payback_period, 1),
                "annual_co2_saved": round(annual_co2_saved, 2),
                "lifetime_co2_saved": round(lifetime_co2_saved, 2),
                "equivalent_trees": round(equivalent_trees, 0)
            }
        }


