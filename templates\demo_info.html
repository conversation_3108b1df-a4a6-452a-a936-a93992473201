{% extends "base.html" %}

{% block title %}Demo Information - Solar Calculator{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <!-- Header -->
        <div class="text-center mb-5">
            <div class="icon-header justify-content-center">
                <i class="fas fa-info-circle" style="font-size: 3rem; color: #2196F3;"></i>
                <h1 class="display-4 fw-bold text-primary ms-3">Demo Information</h1>
            </div>
            <p class="lead text-muted">
                Solar Plant Financial Calculator v2.0 - Complete Feature Overview
            </p>
        </div>

        <!-- What's New -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="icon-header">
                    <i class="fas fa-star" style="color: #FFD700;"></i>
                    <h3 class="mb-0">What's New in Version 2.0</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="text-success">✅ Enhanced Features</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Comprehensive form-based UI</li>
                            <li><i class="fas fa-check text-success me-2"></i>Enhanced calculation formulas</li>
                            <li><i class="fas fa-check text-success me-2"></i>Consumer type-specific pricing</li>
                            <li><i class="fas fa-check text-success me-2"></i>Personalized recommendations</li>
                            <li><i class="fas fa-check text-success me-2"></i>Professional results dashboard</li>
                            <li><i class="fas fa-check text-success me-2"></i>OCR bill processing</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5 class="text-primary">🚀 Full Version Features</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-database text-primary me-2"></i>Supabase backend integration</li>
                            <li><i class="fas fa-download text-primary me-2"></i>PDF report generation</li>
                            <li><i class="fas fa-globe text-primary me-2"></i>Global Solar Atlas API</li>
                            <li><i class="fas fa-chart-bar text-primary me-2"></i>Analytics dashboard</li>
                            <li><i class="fas fa-history text-primary me-2"></i>Calculation history</li>
                            <li><i class="fas fa-cloud text-primary me-2"></i>Cloud data storage</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Calculation Methodology -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="icon-header">
                    <i class="fas fa-calculator settings-icon"></i>
                    <h3 class="mb-0">Enhanced Calculation Methodology</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="text-primary">Core Formulas</h5>
                        <div class="bg-light p-3 rounded">
                            <p><strong>Plant Capacity (kW) =</strong><br>
                            Monthly Consumption ÷ (Avg Irradiance × PR × 30)</p>
                            
                            <p><strong>Monthly Generation =</strong><br>
                            Capacity × Avg Irradiance × PR × 30</p>
                            
                            <p><strong>Investment (CAPEX) =</strong><br>
                            Capacity × Cost per kW</p>
                            
                            <p><strong>Annual Savings =</strong><br>
                            Yearly Generation × Tariff Rate</p>
                            
                            <p><strong>Payback Period =</strong><br>
                            Investment ÷ Annual Savings</p>
                            
                            <p><strong>CO₂ Saved =</strong><br>
                            Yearly Generation × 0.8 kg/kWh</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5 class="text-success">Smart Pricing Matrix</h5>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Consumer Type</th>
                                        <th>System Size</th>
                                        <th>Cost/kW</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td rowspan="3">Residential</td>
                                        <td>&lt; 5 kW</td>
                                        <td>₹80,000</td>
                                    </tr>
                                    <tr>
                                        <td>5-10 kW</td>
                                        <td>₹78,000</td>
                                    </tr>
                                    <tr>
                                        <td>&gt; 10 kW</td>
                                        <td>₹76,000</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="3">Commercial</td>
                                        <td>&lt; 50 kW</td>
                                        <td>₹75,000</td>
                                    </tr>
                                    <tr>
                                        <td>50-100 kW</td>
                                        <td>₹72,000</td>
                                    </tr>
                                    <tr>
                                        <td>&gt; 100 kW</td>
                                        <td>₹70,000</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="3">Industrial</td>
                                        <td>&lt; 100 kW</td>
                                        <td>₹70,000</td>
                                    </tr>
                                    <tr>
                                        <td>100-500 kW</td>
                                        <td>₹68,000</td>
                                    </tr>
                                    <tr>
                                        <td>&gt; 500 kW</td>
                                        <td>₹65,000</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technology Stack -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="icon-header">
                    <i class="fas fa-code settings-icon"></i>
                    <h3 class="mb-0">Technology Stack</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h5 class="text-primary">Frontend</h5>
                        <ul class="list-unstyled">
                            <li><i class="fab fa-html5 text-danger me-2"></i>HTML5 & CSS3</li>
                            <li><i class="fab fa-bootstrap text-primary me-2"></i>Bootstrap 5</li>
                            <li><i class="fab fa-js text-warning me-2"></i>JavaScript ES6+</li>
                            <li><i class="fas fa-icons text-info me-2"></i>Font Awesome Icons</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5 class="text-success">Backend</h5>
                        <ul class="list-unstyled">
                            <li><i class="fab fa-python text-primary me-2"></i>Python 3.12</li>
                            <li><i class="fas fa-flask text-success me-2"></i>Flask Framework</li>
                            <li><i class="fas fa-database text-info me-2"></i>Supabase (PostgreSQL)</li>
                            <li><i class="fas fa-file-pdf text-danger me-2"></i>ReportLab (PDF)</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5 class="text-warning">Integrations</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-globe text-success me-2"></i>Global Solar Atlas API</li>
                            <li><i class="fas fa-eye text-primary me-2"></i>OCR Processing</li>
                            <li><i class="fas fa-chart-line text-info me-2"></i>Plotly Charts</li>
                            <li><i class="fas fa-cloud text-secondary me-2"></i>Cloud Storage</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Setup Instructions -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="icon-header">
                    <i class="fas fa-cog settings-icon"></i>
                    <h3 class="mb-0">Full Version Setup</h3>
                </div>
            </div>
            <div class="card-body">
                <h5 class="text-primary">To enable full features with Supabase:</h5>
                <ol>
                    <li><strong>Create Supabase Project:</strong>
                        <ul>
                            <li>Go to <a href="https://supabase.com" target="_blank">supabase.com</a></li>
                            <li>Create a new project</li>
                            <li>Copy your project URL and anon key</li>
                        </ul>
                    </li>
                    <li><strong>Setup Database:</strong>
                        <ul>
                            <li>Run the SQL schema from <code>backend/database_schema.sql</code></li>
                            <li>This creates tables for calculations and location data</li>
                        </ul>
                    </li>
                    <li><strong>Configure Environment:</strong>
                        <ul>
                            <li>Update <code>.env</code> file with your Supabase credentials</li>
                            <li>Add Global Solar Atlas API key (optional)</li>
                        </ul>
                    </li>
                    <li><strong>Run Full Application:</strong>
                        <ul>
                            <li>Use <code>python solar_app.py</code> instead of <code>demo_app.py</code></li>
                            <li>All features including PDF reports will be available</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center">
            <a href="{{ url_for('index') }}" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-calculator me-2"></i>Try Demo Calculator
            </a>
            <a href="https://github.com/caffehigh/internship-solar-energy" class="btn btn-secondary btn-lg" target="_blank">
                <i class="fab fa-github me-2"></i>View Source Code
            </a>
        </div>
    </div>
</div>
{% endblock %}
